/**
 * each page has different js loaded. e.g. in outpost you will see outpost.js, but in inventory you will see inventory.js, etc
 * userVars contain all information about the user, but its exact information is page dependent too
 *
 * outpost (index.php)
 *  loadSidebar called with hardcoded flashErl
 *  initData called with hardcoded flashErl, then initOutpost is called
 *
 * inventory (index.php?page=25)
 *  initiateInventory called with hardcoded flashErl
 */

/******************** USER SETTINGS ********************
 * default settings are hardcoded
 * user settings are loaded from localStorage: localStorage.getItem("df_html5")
 */
interface UserSettings {
  general: {
    playSound: boolean;
    volume: number;
    displayAvatars: boolean;
    statusPercents: boolean;
    simpleMenus: boolean;
  };
  forum: {
    displayAvatars: boolean;
  };
  gamblingden: {
    instant: boolean;
  };
  inventory: {
    sortstyle: number;
    sortbyscrap: boolean;
  };
  hidden: {
    hidearmour: boolean;
  };
}
var defUserSettings: UserSettings;
var userSettings: UserSettings;

// sets #settingsBox element's src to `hotrods/hotrods_v${hrV}/HTML5/pages/settings.html` and its display to block. i.e. opens the settings page
function loadUserSettings(): void;

// checks if a setting is true in userSettings
function checkLSBool<
  K extends keyof UserSettings,
  NestedKey extends keyof UserSettings[K]
>(boolCat: K, boolName: NestedKey): boolean;

/**
 * Checks whether a property exists in userSettings or defUserSettings. returns true if it exists
 * If the property is missing in userSettings but exists in defUserSettings, it is initialized and persisted to localStorage.
 * reloads the page if defUserSettings is undefined
 */
function checkPropertyValid<
  K extends keyof UserSettings,
  NestedKey extends keyof UserSettings[K]
>(propCat: K, propName: NestedKey): boolean;

// ******************** END USER SETTINGS ********************

/******************** PROMPT ********************
 *
 * div element displaying prompt messages (e.g. loading, are your sure, etc)
 *
 * html structure:
 *    #prompt -> #gamecontent === df_prompt
 */
var df_prompt: DivElement | false;

// displays a prompt with text "Loading..." or customMessage
function promptLoading(customMessage?: string): void;

// Hides and resets the global df_prompt element, clearing its contents and removing highlight classes
function promptEnd(): void;

// ******************** END PROMPT ********************

/******************** NETWORKING AND TRANSPORT ********************
 * most calls need pagetime, sc, userID and password
 *
 * modify_values
 *  used for level up (see checkIfLevelUp)
 *  used for page change (see doPageChange)
 *
 * hotrods/hotrods_avatars/getColorList
 *  no params
 *  returns a list of head / face avatar names?
 *
 * get_storage
 *  need hash
 *  returns empty data in outpost
 *
 */

/**
 * Parses a flash-style string in the format of key=value pairs separated by & into an object
 * if key is in intsToParse, value is parsed as int
 * if padding is provided, it is prefixed to each key
 * if callback is not false, it is called with the parsed object as an argument and the object is not returned. otherwise the object is returned
 */
var intsToParse = [
  "df_cash",
  "df_credits",
  "DFSTATS_df_cash",
  "DFSTATS_df_credits",
  "DFSTATS_df_survival",
] as const;
// function flshToArr(
//   flashStr: string,
//   padding?: string,
//   callback: ((parsed: Record<string, any>) => void) | false = false
// ): Record<string, any> | void;

/**
 * technicall you could pass in a callback and have it not return anything, but I want to force functional programming to be explicit, and easier to work with the types
 */
function flshToArr(flashStr: string, padding?: string): Record<string, any>;

// equivalent to Object.assign(baseArr, flshArr)
function updateIntoArr(
  flshArr: Record<string, any>,
  baseArr: Record<string, any>
): void;

// converts an object into a flash-style string in the format of key=value pairs separated by &
function objectJoin(obj: Record<string, any>): string;

var userVars: Record<string, any> | undefined | [];
// Sets the global userVars object to the provided object
function setUserVars(flshArr: Record<string, any>): void;

/**
 * makes a XMLHttpRequest post call to `${call}.php, sending over params serialized as a flash-style string using objectJoin
 * if hashed is true, a hash of the params is prepended to the request (see md5.js), with key "hash"
 * if callback is not false, it is called sometimes (see checkValidPacket)
 */
function webCall(
  call: string,
  params: Record<string, any>,
  callback?: (data: string, status: number, xhr: XMLHttpRequest) => void,
  hashed?: boolean
): void;

// called by webCall to handle the response from the server. handles errors, logs timings and calls callback
function checkValidPacket(
  callback: (data: string, status: number, xhr: XMLHttpRequest) => void,
  call: string,
  data: string,
  status: number,
  xhr: XMLHttpRequest
): void;

/**
 * shows a prompt with text "Connection Error"
 * then reloads window after a short delay
 */
function webCallError(): void;

function timedReload(timeToReload: number = 2500): void;
function timedRedirect(
  locationToGo: string,
  timeToRedirect: number = 2500
): void;

// ******************** END NETWORKING AND TRANSPORT ********************

/********************* VISUAL & GRAPHICS ********************/

// contains & split output of hotrods/hotrods_avatars/getColorList. with "bald" appended
var noColor: string[] | false;

// update cash elements with the given amount
function updateCashElements(elemClass: string, amt: number): void;

/**
 * renders an avatar into elem using settings provided in customVars
 * this can be called either for the sidebar or in forum
 * the element is always selected via .characterRender
 */
function renderAvatarUpdate(
  elem?: HTMLElement,
  customVars?: Record<string, any>
): void;

function loadSidebar(flashErl: string): void;
function loadSignature(elem: HTMLElement, flashErl: string): void;

// updates all info on the sidebar, using data from userVars
function updateAllFieldsBase(): void;

// basically updateAllFieldsBase() + promptEnd()
function updateAllFields(): void;

// ******************** END VISUAL & GRAPHICS ********************

/********************* AUDIO ********************/

// audioPath = `hotrods/hotrods_v${hrV}/HTML5/sounds/${sound}.${mp3 or ogg}`
function playSound(sound: string): void;

// ******************** END AUDIO ********************

/********************* GAMEPLAY ********************/

// if level up conditions are met, then calls modify_values endpoint and redirects to page 14 after webcall finishes
function checkIfLevelUp(
  neededExp: number,
  currentExp: number,
  freePoints: number,
  curLevel: number,
  password: string,
  userid: string,
  sc: string,
  templateid: string
): void;

// performs various checks and calls webCall to update game state. e.g. death, hunger and item spawn
function baseGameUpdates(
  curHP: number,
  dead: number,
  outpost: number,
  serverTime: number,
  hungerTime: number,
  lastSpawn: number,
  password: string,
  userid: string,
  sc: string,
  templateid: string,
  tts: number
): void;

// maps tradezone id to name
function tradezoneNamer(tradezone: number | string): string;
function tradezoneNamerShort(tradezone: number | string): string;

// creates slot machine element. typically used to put into prompt element (see inventory.js)
function createSlotDisplay(): HTMLDivElement;

// click handler for page change button. uses the dataset property on the element to determine the page to change to. calls doPageChange to perform the change
function nChangePage(e: MouseEvent): void;
// calls webCall to change page
function doPageChange(pageNum: number, modify: string, nTab: boolean): void;

// ******************** END GAMEPLAY ********************

/******************** UTILITY ********************/

// hotrods version, in the form of "x_y_z"
var hrV: string;

var nf: Intl.NumberFormat;

// basically String.split(sep, n), but includes any text cut off from the split in the last element if there are n elements
declare interface String {
  explode(sep: string, n: number): string[];
}

function startsWithVowel(target): boolean;

// returns all elements at a given point, bottom elements first
function allElementsFromPoint(x, y): HTMLElement[];
// returns the first element at a given point that matches delim (top element matched first). if isId is true, delim is matched against element.id, otherwise against element.classList
function specificElementFromPoint(x, y, delim, isId?: boolean): HTMLElement;

// ******************** END POLYFILL ********************

// hardcoded into index.php, but only available for some pages (can polyfill if needed)
var globalData: Record<string, any> | undefined;

var inventoryHolder: HTMLDivElement | undefined;
var infoBox: HTMLDivElement | undefined;

// always available?
var sidebar: HTMLDivElement | undefined;
