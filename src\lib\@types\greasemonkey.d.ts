var unsafeWindow: any;

namespace GM {
  function xmlHttpRequest(details: {
    url: string;
    method: "GET" | "POST";
    binary?: boolean;
    context?: any;
    data?: string;
    headers?: Record<string, string>;
    data?: any;
    onabort?: (response: any) => void;
    onerror?: (response: any) => void;
    onload?: (response: any) => void;
    onprogress?: (response: any) => void;
    onreadystatechange?: (response: any) => void;
    ontimeout?: (response: any) => void;
  }): void;
}
