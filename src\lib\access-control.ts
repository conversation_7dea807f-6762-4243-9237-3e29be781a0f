/**
 * procedure:
 * - create a pastebin with content "1"
 * - obfuscate output js using https://obfuscator.io. disable "debug protection"
 * - ask for feedback / payment, then once received, give the unobfuscated code
 * - if no payment, change pastebin content to "2"
 */

import { gmFetch } from "@/lib/gm";

export async function accessControl(rawUrl): Promise<string> {
  return await gmFetch({
    url: "https://pastebin.com/raw/12345678",
    method: "GET",
  });
}
