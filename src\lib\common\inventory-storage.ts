/**
 * slot numbering
 *  player inventory (df_inv): 1-30
 *  player equipment: 31-40
 *  storage (storageBox): 40 + data-slot (e.g. top left of storage is 41)
 *  implant slots (df_implant): 1000 + data-slot (e.g. top left of implant grid is 1001)
 *  implant storage: not in the number system, it uses a different call
 *  backpack (df_backpack): 1050 + data-slot (e.g. top left of backpack is 1051)
 *
 * generic slots means df_inv, df_implant, df_backpack
 *
 * userVars["DFSTATS_df_${SlotType}slots"]
 * userVars["DFSTATS_df_${SlotType}${i}_type"]
 * userVars["DFSTATS_df_${SlotType}${i}_quantity"]
 *
 * storageBox["df_store${i}_type"]
 * storageBox["df_store${i}_quantity"]
 *
 * userVars["DFSTATS_df_backpack"] is the backpack type
 * backpack slots need to be calculated based on backpack type
 * userVars["DFSTATS_df_backpack${i}_type"]
 * userVars["DFSTATS_df_backpack${i}_quantity"]
 *
 * userVars["DFSTATS_df_implantslots"]
 * userVars["DFSTATS_df_implant${i}_type"]
 *
 * function findInInventory(itemToFind)
 * class InventoryItem
 */

import { getTradeZone, isIronman } from "@/lib/common/character";
import { APIFacet, PAGE } from "@/lib/common/webcall";
import { MarketCategory } from "../market";

declare global {
  // reloads inventory data from server, then update UI
  function reloadInventoryData(): void;
  function reloadStorageData(invData: string): void;

  // sets DOM inventory slots using userVars
  function populateInventory(): void;
  // sets DOM character slots (equipped items) using userVars
  function populateCharacterInventory(): void;
  // sets DOM implant slots using userVars
  function populateImplants(): void;
  function populateStorage(): void;

  // itemType should be the full, un-split "type" attribute on item
  function scrapValue(itemType: string, quantity: number): number;

  // not sure what this value is, but needed in trade calls
  function scrapAmount(itemType: string, quantity: number): number;

  // returns the price to upgrade storage
  function getUpgradePrice(): number;

  // use the corresponding methods on classes instead
  // function findFirstEmptyStorageSlot(): number | false;
  // function findLastEmptyStorageSlot(): number | false;
  // function findFirstEmptyGenericSlot(slotType: string): number | false;
  // function findLastEmptyGenericSlot(slotType: string): number | false;
  // function findFirstEmptyBackpackSlot(): number | false;

  // [x, y] from last event clientX, clientY
  var mousePos: number[];

  var storageBox: Record<string, any>;

  var lockedSlots: string[];
}

export class Slot {
  static readonly CHRACTER_SLOT_START = 31;
  static readonly WEAPON_1_SLOT = 31;
  static readonly WEAPON_2_SLOT = 32;
  static readonly WEAPON_3_SLOT = 33;
  static readonly ARMOUR_SLOT = 34;
  static readonly BACKPACK_SLOT = 35;
  static readonly SHIRT_SLOT = 36;
  static readonly TROUSERS_SLOT = 37;
  static readonly COAT_SLOT = 38;
  static readonly MASK_SLOT = 39;
  static readonly HAT_SLOT = 40;

  static readonly STORAGE_SLOT_OFFSET = 40;
  static readonly IMPLANT_SLOT_OFFSET = 1000;
  static readonly BACKPACK_SLOT_OFFSET = 1050;

  // this is the slot number used to make calls, not dataset.slot / data-slot from DOM
  readonly slotNum: number;

  constructor(slotNum: any) {
    this.slotNum = parseInt(slotNum);
  }

  static fromElement(element: HTMLElement): Slot | null {
    // slot element may not necessarily be DIV
    while (element) {
      if (element.dataset["slot"]) {
        break;
      }
      element = element.parentElement;
    }
    if (!element) {
      return null;
    }

    const elementSlotNum = element.dataset["slot"];

    // need to check if it's in storage or implant
    if (element.closest("#storage")) {
      return new Slot(parseInt(elementSlotNum) + Slot.STORAGE_SLOT_OFFSET);
    } else if (element.closest("#implants")) {
      return new Slot(parseInt(elementSlotNum) + Slot.IMPLANT_SLOT_OFFSET);
    }

    return new Slot(elementSlotNum);
  }

  private _item: Item | null = null;
  get item() {
    if (!this._item) {
      this._item = this.getItem();
    }
    return this._item;
  }

  isLocked() {
    return lockedSlots.includes(this.slotNum.toString());
  }

  isInventory() {
    return this.slotNum > 0 && this.slotNum <= Inventory.numSlots();
  }

  isCharacter() {
    return (
      this.slotNum >= Slot.CHRACTER_SLOT_START &&
      this.slotNum <= Slot.STORAGE_SLOT_OFFSET
    );
  }

  isStorage() {
    return (
      this.slotNum > Slot.STORAGE_SLOT_OFFSET &&
      this.slotNum <= Slot.STORAGE_SLOT_OFFSET + Storage.numSlots()
    );
  }

  isImplant() {
    return (
      this.slotNum > Slot.IMPLANT_SLOT_OFFSET &&
      this.slotNum <= Slot.IMPLANT_SLOT_OFFSET + Implant.numSlots()
    );
  }

  isBackpack() {
    return (
      this.slotNum > Slot.BACKPACK_SLOT_OFFSET &&
      this.slotNum <= Slot.BACKPACK_SLOT_OFFSET + Backpack.numSlots()
    );
  }
  /**
   * returns null if slot is empty. throws if slot number is out of range
   */
  private getItem(): Item | null {
    if (this.isInventory()) {
      return Inventory.getItem(this.slotNum);
    }

    if (this.isCharacter()) {
      const characterBodyItem = {
        [Slot.WEAPON_1_SLOT]: [userVars["DFSTATS_df_weapon1type"], 1],
        [Slot.WEAPON_2_SLOT]: [userVars["DFSTATS_df_weapon2type"], 1],
        [Slot.WEAPON_3_SLOT]: [userVars["DFSTATS_df_weapon3type"], 1],
        [Slot.ARMOUR_SLOT]: [
          userVars["DFSTATS_df_armourtype"],
          userVars["DFSTATS_df_armourhp"],
        ],
        [Slot.BACKPACK_SLOT]: [userVars["DFSTATS_df_backpack"], 1],
        [Slot.SHIRT_SLOT]: [userVars["DFSTATS_df_avatar_shirt"], 1],
        [Slot.TROUSERS_SLOT]: [userVars["DFSTATS_df_avatar_trousers"], 1],
        [Slot.COAT_SLOT]: [userVars["DFSTATS_df_avatar_coat"], 1],
        [Slot.MASK_SLOT]: [userVars["DFSTATS_df_avatar_mask"], 1],
        [Slot.HAT_SLOT]: [userVars["DFSTATS_df_avatar_hat"], 1],
      }[this.slotNum];

      if (characterBodyItem[0] === "") {
        return null;
      }
      return new Item(characterBodyItem[0], characterBodyItem[1], false);
    }

    if (this.isStorage()) {
      return Storage.getItem(this.slotNum - 40);
    }

    if (this.isImplant()) {
      return Implant.getItem(this.slotNum - 1000);
    }

    if (this.isBackpack()) {
      return Backpack.getItem(this.slotNum - 1050);
    }

    throw new Error(`Slot number ${this.slotNum} out of range`);
  }
}

export class Item {
  quantity: number;
  // captures all important states
  type: string;
  // like category. can be missing from fake items
  itemtype: string;

  // the "type" attribute, or first part of the "type" attribute
  dataKey: string;
  // second part of the "type" attribute
  details: string[];
  // object form of details, with keys being one of KNOWN_DETAIL_PREFIXES
  detailsObj: Partial<
    Record<(typeof Item.KNOWN_DETAIL_PREFIXES)[number], string>
  >;
  static KNOWN_DETAIL_PREFIXES = ["stats", "colour"] as const;
  // retrieved data from globalData
  data: any;

  slot: string | undefined;
  fakeItem: boolean;

  constructor(type: string, quantity: any = 1, isFakeItem: boolean = false) {
    this.type = type;
    const rawTypeArr = this.type.split("_");
    this.dataKey = rawTypeArr[0];
    this.data = globalData[this.dataKey];

    this.details = rawTypeArr.slice(1);
    this.detailsObj = {};
    for (const detail of this.details) {
      for (const prefix of Item.KNOWN_DETAIL_PREFIXES) {
        if (detail.startsWith(prefix)) {
          this.detailsObj[prefix] = detail.substring(prefix.length);
          break;
        }
      }
    }

    this.quantity = parseInt(quantity);

    // even though dataset can have "itemtype" field, it's not accurate
    // e.g. for clothing its "itemtype" field on the dataset will actually have the value of "clothingtype" from globalData
    // this.itemtype = this.element.dataset["itemtype"];
    this.itemtype = this.data["itemtype"];

    this.fakeItem = isFakeItem;
  }

  // search up the DOM tree for item element
  static fromElement(element: HTMLElement): Item | null {
    while (element) {
      if (
        element.tagName === "DIV" &&
        (element.classList.contains("fakeItem") ||
          element.classList.contains("item"))
      ) {
        break;
      }
      element = element.parentElement;
    }
    if (!element) {
      return null;
    }
    return new Item(
      element.dataset["type"],
      element.dataset["quantity"],
      element.classList.contains("fakeItem")
    );
  }

  static getServiceLevel(itemData: Record<string, any>): number {
    if ("shop_level" in itemData) {
      return itemData["shop_level"] - (isIronman() ? 10 : 5);
    } else if ("level" in itemData) {
      return itemData["level"] - (isIronman() ? 10 : 5);
    }
    throw new Error(`Item cannot be serviced: ${JSON.stringify(itemData)}`);
  }

  _marketCategory: string | null = null;
  get marketCategory() {
    if (this._marketCategory) {
      return this._marketCategory;
    }
    this._marketCategory = Item.toMarketCategory(this);
    return this._marketCategory;
  }

  _scrapValue: number | null = null;
  get scrapValue() {
    if (this._scrapValue) {
      return this._scrapValue;
    }
    this._scrapValue = scrapValue(this.type, this.quantity);
    return this._scrapValue;
  }

  /**
   * adapted from Dead Frontier - API by Shrike00
   */
  static toMarketCategory(item: Item): MarketCategory {
    if (item.itemtype === "weapon") {
      const weapon_type = item.data["type"];
      if (["submachinegun", "machinegun"].includes(weapon_type)) {
        return "weapon_lightmachinegun";
      } else if (["bigmachinegun", "minigun"].includes(weapon_type)) {
        return "weapon_heavymachinegun";
      } else if (weapon_type === "grenadelauncher") {
        return "weapon_grenadelauncher";
      } else {
        return ("weapon_" + item.data["pro_type"]) as MarketCategory;
      }
    }

    if (item.itemtype === "armour") {
      return "armour";
    }

    if (item.itemtype === "ammo") {
      if (item.type.indexOf("rifle") !== -1) {
        return "ammo_rifle";
      } else if (item.type.indexOf("gauge") !== -1) {
        return "ammo_shotgun";
      } else if (item.type.indexOf("grenade") !== -1) {
        return "ammo_grenade";
      } else if (
        ["energycellammo", "biomassammo", "fuelammo"].includes(item.type)
      ) {
        return "ammo_special";
      } else {
        return "ammo_handgun";
      }
    }

    if (item.itemtype === "item") {
      if (parseInt(item.data["foodrestore"]) > 0) {
        return "food";
      }
      if (parseInt(item.data["healthrestore"]) > 0) {
        return "medical";
      }
      if (item.data["clothingtype"]) {
        if (["mask", "hat"].includes(item.data["clothingtype"])) {
          return "clothing_headwear";
        } else if (item.data["clothingtype"] === "coat") {
          return "clothing_coat";
        } else {
          return "clothing_basic";
        }
      }
      if (parseInt(item.data["barricade"]) === 1) {
        return "barricading";
      }
      if (item.data["implant"]) {
        return "implants";
      }
    }

    return "misc";
  }
}

export class Backpack {
  static numSlots(): number {
    const backpackType = userVars["DFSTATS_df_backpack"];
    if (backpackType === "") {
      return 0;
    }
    const backpackItem = new Item(backpackType, 1, false);

    let slots = backpackItem.data["slots"];
    if ("stats" in backpackItem.detailsObj) {
      slots += parseInt(backpackItem.detailsObj["stats"]);
    }

    return slots;
  }

  static getAllSlots(): Slot[] {
    return Array.from(
      { length: this.numSlots() },
      (_, i) => new Slot(i + 1 + Slot.BACKPACK_SLOT_OFFSET)
    );
  }

  static getItem(slotNum: any): Item | null {
    if (slotNum < 1 || slotNum > this.numSlots()) {
      return null;
    }

    if (
      userVars[`DFSTATS_df_backpack${slotNum}_type`] == null ||
      userVars[`DFSTATS_df_backpack${slotNum}_type`] === ""
    ) {
      return null;
    }

    return new Item(
      userVars[`DFSTATS_df_backpack${slotNum}_type`],
      userVars[`DFSTATS_df_backpack${slotNum}_quantity`],
      false
    );
  }
}

export class Implant {
  static numSlots(): number {
    return parseInt(userVars["DFSTATS_df_implantslots"]);
  }

  static getAllSlots(): Slot[] {
    return Array.from(
      { length: this.numSlots() },
      (_, i) => new Slot(i + 1 + Slot.IMPLANT_SLOT_OFFSET)
    );
  }

  static findFirstEmptySlot(): Slot | null {
    return this.getAllSlots().find((slot) => !slot.item);
  }

  static getItem(slotNum: any): Item | null {
    if (slotNum < 1 || slotNum > this.numSlots()) {
      return null;
    }

    if (userVars[`DFSTATS_df_implant${slotNum}_type`] === "") {
      return null;
    }

    return new Item(userVars[`DFSTATS_df_implant${slotNum}_type`], 1, false);
  }
}

export class Storage {
  /**
   * can be called from inventory
   */
  static async loadStorageData() {
    const result = await APIFacet.call(
      Storage.loadStorageData,
      "get_storage",
      {}
    );
    unsafeWindow.storageBox = flshToArr(result);
    if (document.getElementById("storage")) {
      populateStorage();
    }
    return result;
  }

  static async takeAllFromStorage() {
    let dataArr = {
      action: "fromstorage",
      slotnum: 1,
    };
    playSound("swap");
    promptLoading();

    const result = await APIFacet.call(
      this.takeAllFromStorage,
      "hotrods/inventory_actions",
      dataArr
    );
    promptEnd();
    updateIntoArr(flshToArr(result, "DFSTATS_"), userVars);
    populateInventory();
  }

  static numSlots(): number {
    return parseInt(userVars["DFSTATS_df_storage_slots"]);
  }

  static getAllSlots(): Slot[] {
    return Array.from(
      { length: this.numSlots() },
      (_, i) => new Slot(i + 1 + Slot.STORAGE_SLOT_OFFSET)
    );
  }

  static findFirstEmptySlot(): Slot | null {
    return this.getAllSlots().find((slot) => !slot.item);
  }

  /**
   * @param slotNum 1 indexed, if you have absolute slot number, subtract Slot.STORAGE_SLOT_OFFSET
   */
  static getItem(slotNum: any): Item | null {
    if (slotNum < 1 || slotNum > this.numSlots()) {
      return null;
    }

    if (
      storageBox[`df_store${slotNum}_type`] == null ||
      storageBox[`df_store${slotNum}_type`] === ""
    ) {
      return null;
    }

    return new Item(
      storageBox[`df_store${slotNum}_type`],
      storageBox[`df_store${slotNum}_quantity`],
      true
    );
  }
}
APIFacet.registerAPI(Storage.loadStorageData, [PAGE.INVENTORY]);

export class Inventory {
  static async moveAllToStorage() {
    let dataArr = {
      action: "tostorage",
      slotnum: 1,
    };
    playSound("swap");
    promptLoading();

    const result = await APIFacet.call(
      this.moveAllToStorage,
      "hotrods/inventory_actions",
      dataArr
    );
    promptEnd();
    updateIntoArr(flshToArr(result, "DFSTATS_"), userVars);
    populateInventory();
  }

  static numSlots(): number {
    return parseInt(userVars["DFSTATS_df_invslots"]);
  }

  // 1 indexed
  static getItem(slotNum: any): Item | null {
    if (slotNum < 1 || slotNum > this.numSlots()) {
      return null;
    }
    if (userVars[`DFSTATS_df_inv${slotNum}_type`] === "") {
      return null;
    }
    return new Item(
      userVars[`DFSTATS_df_inv${slotNum}_type`],
      userVars[`DFSTATS_df_inv${slotNum}_quantity`],
      false
    );
  }

  static getAllSlots(): Slot[] {
    return Array.from({ length: this.numSlots() }, (_, i) => new Slot(i + 1));
  }

  static getAllItems(): (Item | null)[] {
    return Array.from({ length: this.numSlots() }, (_, i) =>
      this.getItem(i + 1)
    );
  }

  static numEmptySlots(): number {
    return this.getAllSlots().filter((slot) => !slot.item).length;
  }

  static hasEmptySlot(): boolean {
    // freeInventorySpace is not reliable
    // return !!unsafeWindow.freeInventorySpace;
    return this.numEmptySlots() > 0;
  }

  static findFirstEmptySlot(): Slot | null {
    return this.getAllSlots().find((slot) => !slot.item);
  }
}

/**
 * equivalent to dragging the item from "from" slot to "to" slot
 * order matters for some calls, e.g. hotrods/backpack and storage - empty slot can be "from" slot otherwise it does nothing
 */
export async function swapSlot(from: Slot, to: Slot) {
  if (
    // one of the slot is implant
    (from.isImplant() || to.isImplant()) &&
    // at leats one of them has non-implant item
    ((from.item != null && !from.item.data["implant"]) ||
      (to.item != null && !to.item.data["implant"]))
  ) {
    throw new Error("Cannot swap implant slot with non-empty non-implant slot");
  }

  if (
    // one of the slot is backpack
    (from.isBackpack() || to.isBackpack()) &&
    // at least one of the slot is not backpack nor inventory
    ((!from.isBackpack() && !from.isInventory()) ||
      (!to.isBackpack() && !to.isInventory()))
  ) {
    throw new Error(
      "Backpack slot can only be swapped another backpack slot, or inventory slot"
    );
  }

  if (
    from.isStorage() ||
    (to.isStorage() && unsafeWindow.location.href.indexOf("page=50") === -1)
  ) {
    // note put all / retrieve all still works outside of storage page
    throw new Error("Storage swapping can only be used in storage page");
  }

  let call = "inventory_new";
  let action = "newswap";

  if (from.isBackpack() || to.isBackpack()) {
    action = "backpack";
    call = "hotrods/backpack";
  } else if (from.isStorage() && to.isStorage()) {
    action = "swapstorage";
  } else if (from.isStorage() && !to.isStorage()) {
    action = "take";
  } else if (!from.isStorage() && to.isStorage()) {
    action = "store";
  }

  const dataArr = {};

  dataArr["creditsnum"] = userVars["DFSTATS_df_credits"];
  dataArr["buynum"] = "0";
  dataArr["renameto"] = "undefined`undefined";
  dataArr["expected_itemprice"] = "-1";
  dataArr["expected_itemtype2"] = to.item?.type ?? "";
  dataArr["expected_itemtype"] = from.item?.type ?? "";
  // expects numbers here
  dataArr["itemnum2"] = to.slotNum;
  dataArr["itemnum"] = from.slotNum;
  dataArr["price"] = getUpgradePrice();
  dataArr["action"] = action;

  const result = await APIFacet.call(swapSlot, call, dataArr);
  updateIntoArr(flshToArr(result, "DFSTATS_"), userVars);
  populateInventory();
  populateCharacterInventory();

  if (document.getElementById("implants")) {
    populateImplants();
  }

  if (from.isStorage() || to.isStorage()) {
    Storage.loadStorageData();
  }

  renderAvatarUpdate();

  return result;
}

export async function scrapItem(slot: Slot) {
  if (getTradeZone() !== "21") {
    throw new Error("Scrap item can only be used in outpost trade zone");
  }

  let dataArr = {};

  dataArr["creditsnum"] = 0;
  dataArr["buynum"] = 0;
  dataArr["renameto"] = "";
  dataArr["expected_itemprice"] = "-1";
  dataArr["expected_itemtype2"] = "";
  dataArr["expected_itemtype"] = slot.item.type;
  dataArr["itemnum2"] = 0;
  dataArr["itemnum"] = slot.slotNum;
  dataArr["price"] = scrapValue(slot.item.type, slot.item.quantity);
  dataArr["action"] = "scrap";

  const result = await APIFacet.call(scrapItem, "inventory_new", dataArr);
  updateIntoArr(flshToArr(result, "DFSTATS_"), userVars);
  populateInventory();
  populateCharacterInventory();
  updateAllFieldsBase();

  return result;
}

export function getCashProtectAmount(): number {
  let cashProtect = 0;

  const allSlots = [...Inventory.getAllSlots(), ...Backpack.getAllSlots()];
  for (const slot of allSlots) {
    if (slot.item && slot.item.data["cashprotect"]) {
      cashProtect = Math.max(
        cashProtect,
        parseInt(slot.item.data["cashprotect"])
      );
    }
  }

  return cashProtect;
}

/**
 * can be called from outpost
 * loads inventory, body items and backpack items
 */
export async function loadInventoryData() {
  const result = await APIFacet.call(loadInventoryData, "get_values");
  updateIntoArr(flshToArr(result, "DFSTATS_"), userVars);
  if (unsafeWindow.populateInventory) {
    populateInventory();
  }
  updateAllFields();
  renderAvatarUpdate();
}
