import { h } from "@/lib/common/ui";

type Content = string | HTMLElement;

function stylePrompt() {
  const container = document.getElementById("prompt");
  container.style.display = "flex";
  container.style.flexDirection = "column";
  container.style.alignItems = "center";
  container.style.justifyContent = "center";

  const innerBox = document.getElementById("gamecontent");
  innerBox.style.position = "relative";
  innerBox.style.width = "auto";
  // innerBox.style.maxWidth = "50%";
  innerBox.style.height = "auto";
  innerBox.style.minHeight = "100px";
  innerBox.style.left = "0";
  innerBox.style.top = "0";
  innerBox.style.padding = "10px";
}

function appendButtons(...buttons: Array<HTMLElement>) {
  const buttonContainer = (
    <div
      style={{
        display: "flex",
        flexDirection: "row",
        justifyContent: "space-around",
        width: "100%",
      }}
    ></div>
  );

  const row = <div></div>;
  for (const button of buttons) {
    button.style.marginRight = "25px";
    row.appendChild(button);
  }

  buttonContainer.appendChild(row);
  df_prompt.appendChild(<br></br>);
  df_prompt.appendChild(buttonContainer);
}

export function promptShow(content?: Content) {
  stylePrompt();

  if (content) {
    if (typeof content === "string") {
      df_prompt.innerHTML = content;
    } else {
      df_prompt.appendChild(content);
    }
  }
}

export function promptOk(content: Content) {
  promptShow(content);
  appendButtons(<button onClick={promptEnd}>OK</button>);
}

export function promptYesNo(
  content: Content,
  onYes: () => void,
  onNo?: () => void
) {
  promptShow(content);
  appendButtons(
    <button onClick={onYes}>Yes</button>,
    <button onClick={onNo ?? promptEnd}>No</button>
  );
}
