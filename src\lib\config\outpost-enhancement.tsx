/**
 * remove button delay (1s delay)
 * add security box cash check warning when going out to innercity
 * launch standalone client upon clicking innercity button
 */

import { getCash, isHardcore } from "@/lib/common/character";
import { getCashProtectAmount } from "@/lib/common/inventory-storage";
import { promptYesNo } from "@/lib/common/prompt";
import { h } from "@/lib/common/ui";

function launchGame() {
  doPageChange(21, "1", false);
}

/**
 * @returns true if we should proceed to innercity
 */
function checkCashProtect(): boolean {
  const cash = getCash();
  const cashProtect = getCashProtectAmount();
  const unprotectedCash = cash - cashProtect;

  if (cash > cashProtect) {
    promptYesNo(
      <div
        style={{
          color: "white",
          padding: "10px",
        }}
      >
        You have{" "}
        <span style={{ color: "red" }}>${nf.format(unprotectedCash)}</span>{" "}
        unprotected cash.<br></br>
        <br></br>
        Are you sure you want to proceed?
      </div>,
      launchGame
    );
    return false;
  }

  return true;
}

/**
 * removes button delay, and add cash warning to innercity button
 */
export function enhanceOutpostButtons() {
  const outpostLinkButtons = [];
  for (const elem of document.getElementsByClassName("opElem")) {
    if (elem.childElementCount === 1) {
      const maybeButton = elem.children[0] as HTMLElement;
      if (
        maybeButton.tagName === "BUTTON" &&
        maybeButton.dataset &&
        maybeButton.dataset["page"]
      ) {
        outpostLinkButtons.push(maybeButton);
      }
    }
  }

  // hide original cash warning
  document.getElementById("textAddon").style.visibility = "hidden";

  for (const button of outpostLinkButtons) {
    button.removeEventListener("mousedown", nChangePage);

    button.addEventListener("mousedown", (e: MouseEvent) => {
      if (!(e.currentTarget instanceof HTMLElement)) {
        return;
      }

      let elem = e.currentTarget;
      if (!elem || (e.which !== 1 && e.which !== 2)) {
        return;
      }

      let nTab = e.which === 2;
      if (nTab) {
        e.preventDefault();
      }

      // going into innercity, check cash and show prompt if necessary
      if (elem.dataset["page"] === "21") {
        if (!checkCashProtect()) {
          return;
        } else {
          launchGame();
          return;
        }
      }

      doPageChange(parseInt(button.dataset.page), button.dataset.mod, false);
    });
  }
}
