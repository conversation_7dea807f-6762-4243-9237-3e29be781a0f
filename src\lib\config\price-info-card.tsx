import { is<PERSON><PERSON><PERSON> } from "@/lib/common/character";
import { Item } from "@/lib/common/inventory-storage";
import { h } from "@/lib/common/ui";
import { fetchMarketData, MarketItemListing } from "@/lib/market";

const DELAY_SEARCH_ON_HOVER = 500;

let currentTaskId = 0;
let timeout = null;
let searchedItem: string | null = null;

function appendLoadingToInfoBox(item: Item) {
  let priceInfoElement: HTMLDivElement = (
    <div
      className="itemData"
      style={{ fontStyle: "italic", textAlign: "left" }}
    >
      Loading price information...
    </div>
  );

  const moveStorageTooltip = document.getElementById("move-storage-tooltip");
  if (moveStorageTooltip) {
    moveStorageTooltip.before(priceInfoElement, <br></br>);
  } else {
    infoBox.appendChild(<br></br>);
    infoBox.appendChild(priceInfoElement);
    // add extra line break to show the "hold s to split" and morse code
    if (["ammo", "credits"].includes(item.data["itemtype"])) {
      infoBox.appendChild(<br></br>);
    }
  }
  return priceInfoElement;
}

interface PriceInfo {
  item: string;
  price: number;
  quantity: number | null;
  pricePer: number | null;
}

function computeUndercut(item: Item, topPriceInfo: PriceInfo): PriceInfo {
  if (["ammo", "credits"].includes(item.itemtype)) {
    const totalPrice =
      Math.floor(topPriceInfo.pricePer * item.quantity) - UNDERCUT_AMOUNT;
    return {
      item: item.type,
      price: totalPrice,
      quantity: item.quantity,
      pricePer: +(totalPrice / item.quantity).toFixed(2),
    };
  } else {
    return {
      item: item.type,
      price: topPriceInfo.price - UNDERCUT_AMOUNT,
      quantity: null,
      pricePer: null,
    };
  }
}

function makePriceInfoElement(
  item: Item,
  priceInfo: PriceInfo,
  prefix: string
): HTMLDivElement {
  const element = (
    <div>
      {prefix}{" "}
      <span
        style={{
          color:
            priceInfo.price < scrapValue(priceInfo.item, priceInfo.quantity)
              ? "red"
              : "white",
        }}
      >
        ${nf.format(priceInfo.price)}
      </span>
    </div>
  );

  if (item.quantity && item.data["max_quantity"] && priceInfo.pricePer) {
    const quantity = priceInfo.quantity;
    const max_quantity = item.data["max_quantity"];

    const unitPriceElement = (
      <span>
        {" ("}
        <span
          style={{
            color:
              quantity > max_quantity
                ? "green"
                : quantity < max_quantity
                ? "yellow"
                : "white",
          }}
        >
          {nf.format(quantity)}
        </span>
        {" @ "}
        {priceInfo.pricePer}
        {" per)"}
      </span>
    );
    element.appendChild(unitPriceElement);
  }

  return element;
}

/**
 * displays top 5 results into the info box
 */
const NUM_DISPLAY_RESULTS = 5;
const UNDERCUT_AMOUNT = 1;
function displayMarketDataInInfoBox(
  item: Item,
  priceInfoElement: HTMLDivElement,
  result: MarketItemListing[]
) {
  priceInfoElement.textContent = "";

  const priceInfos: PriceInfo[] = [];

  for (const info of result.slice(0, NUM_DISPLAY_RESULTS)) {
    priceInfos.push({
      item: info.item,
      price: info.price,
      quantity: info.quantity,
      pricePer: info.priceper,
    });
  }

  if (priceInfos.length === 0) {
    priceInfoElement.textContent = "No results found";
    return;
  }

  priceInfoElement.appendChild(
    makePriceInfoElement(item, computeUndercut(item, priceInfos[0]), "cut:")
  );
  for (let i = 0; i < priceInfos.length; i++) {
    priceInfoElement.appendChild(
      makePriceInfoElement(
        item,
        priceInfos[i],
        `${i === 0 ? "1st:" : `${i + 1}th:`}`
      )
    );
  }
}

function repositionInfoBox(target: HTMLElement) {
  var invHoldOffsets = inventoryHolder.getBoundingClientRect();
  var specialPlacement = false;
  if (mousePos[1] - 30 - infoBox.offsetHeight < invHoldOffsets.top) {
    //infoBox.style.top = (mousePos[1] + 30 - invHoldOffsets.top) + "px";
    if ((target.parentNode.parentNode as HTMLDivElement).id === "venditron") {
      infoBox.style.top = "17px";
    } else {
      infoBox.style.top = "0px";
    }
    specialPlacement = true;
  } else {
    infoBox.style.top =
      mousePos[1] - 30 - infoBox.offsetHeight - invHoldOffsets.top + "px";
  }

  if (mousePos[0] + 20 + infoBox.offsetWidth > invHoldOffsets.right) {
    if (specialPlacement) {
      infoBox.style.left =
        mousePos[0] - 20 - infoBox.offsetWidth - invHoldOffsets.left + "px";
    } else {
      // original
      infoBox.style.left =
        inventoryHolder.offsetWidth - infoBox.offsetWidth + "px";
    }
  } else {
    infoBox.style.left = mousePos[0] + 20 - invHoldOffsets.left + "px";
  }
}

async function executeSearch(
  executionTaskId: number,
  item: Item,
  target: HTMLElement
) {
  // some time has already elapsed, need to check if we're still interested in this task
  if (executionTaskId !== currentTaskId) {
    return;
  }

  // show loading message and fetch data
  const priceInfoElement = appendLoadingToInfoBox(item);
  repositionInfoBox(target);
  const result = await fetchMarketData(item);

  // check if we're still interested in this task
  if (executionTaskId !== currentTaskId) {
    return;
  }

  displayMarketDataInInfoBox(
    item,
    priceInfoElement,
    result as MarketItemListing[]
  );
  repositionInfoBox(target);
}

function mouseMoveHandler(e: MouseEvent) {
  if (!(e.target instanceof HTMLElement)) {
    return;
  }

  // if the info card is not visible, clear states
  if (infoBox.style.visibility !== "visible") {
    if (timeout) {
      clearTimeout(timeout);
      timeout = null;
      searchedItem = null;
      currentTaskId++;
    }
    return;
  }

  const item = Item.fromElement(e.target as HTMLElement);
  if (!item || item.fakeItem || item.data["no_transfer"]) {
    return;
  }

  // if we have a pending task for this item, don't do anything
  if (timeout !== null && item.type === searchedItem) {
    return;
  }

  // schedule task execution
  const executionTaskId = currentTaskId;
  timeout = setTimeout(async () => {
    await executeSearch(executionTaskId, item, e.target as HTMLElement);
  }, DELAY_SEARCH_ON_HOVER);
  searchedItem = item.type;
}

export function augmentInfoCardWithPriceInfo() {
  if (isIronman()) {
    return;
  }

  inventoryHolder.addEventListener("mousemove", mouseMoveHandler);
}
