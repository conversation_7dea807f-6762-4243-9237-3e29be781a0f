export function saveGlobalVarsToLocalStorageOnUnload() {
  unsafeWindow.addEventListener("beforeunload", () => {
    localStorage.setItem("globalVarsTime", Date.now().toString());
    if (unsafeWindow.userVars) {
      localStorage.setItem("userVars", JSON.stringify(unsafeWindow.userVars));
    }
    if (unsafeWindow.globalData) {
      localStorage.setItem(
        "globalData",
        JSON.stringify(unsafeWindow.globalData)
      );
    }
    if (unsafeWindow.storageBox) {
      localStorage.setItem(
        "storageBox",
        JSON.stringify(unsafeWindow.storageBox)
      );
    }
    if (unsafeWindow.implantStorage) {
      localStorage.setItem(
        "implantStorage",
        JSON.stringify(unsafeWindow.implantStorage)
      );
    }
    if (unsafeWindow.implantPresets) {
      localStorage.setItem(
        "implantPresets",
        JSON.stringify(unsafeWindow.implantPresets)
      );
    }
  });
}

function loadCachedGlobalVarsFromLocalStorage() {
  const globalVarsTime = localStorage.getItem("globalVarsTime");

  // only load from local storage if it's less than 5 minutes old
  if (globalVarsTime && Date.now() - parseInt(globalVarsTime) > 1000 * 60 * 5) {
    return;
  }

  unsafeWindow.userVars = {
    ...JSON.parse(localStorage.getItem("userVars") ?? "{}"),
    ...unsafeWindow.userVars,
  };
  unsafeWindow.globalData = {
    ...JSON.parse(localStorage.getItem("globalData") ?? "{}"),
    ...unsafeWindow.globalData,
  };
  unsafeWindow.storageBox = {
    ...JSON.parse(localStorage.getItem("storageBox") ?? "{}"),
    ...unsafeWindow.storageBox,
  };
  unsafeWindow.implantStorage = {
    ...JSON.parse(localStorage.getItem("implantStorage") ?? "{}"),
    ...unsafeWindow.implantStorage,
  };
  unsafeWindow.implantPresets = [
    ...JSON.parse(localStorage.getItem("implantPresets") ?? "[]"),
    ...(unsafeWindow.implantPresets ?? []),
  ];
}

export function loadGlobalVarsFromLocalStorage() {
  loadCachedGlobalVarsFromLocalStorage();

  if (!unsafeWindow.globalData) {
    unsafeWindow.globalData = JSON.parse(
      localStorage.getItem("cachedGlobalData") ?? "{}"
    );
  }
}
