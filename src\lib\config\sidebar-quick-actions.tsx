/**
 * limited to marketplace since buy calls only take affect in marketplace
 */

import { getCash, getMaxHunger, useItem } from "@/lib/common/character";
import { Inventory, Item, Slot } from "@/lib/common/inventory-storage";
import { Log } from "@/lib/common/log";
import { h, makeColoredPriceBasedOnCash } from "@/lib/common/ui";
import {
  buyItem,
  buyService,
  fetchMarketData,
  fetchServicePrices,
  getCheapestService,
  MarketItemListing,
  MarketServiceListing,
} from "@/lib/market";

const DELAY_SEARCH_ON_HOVER = 300;

let timeout = null;
let currentTaskId = 0;

let currentDeal: ServiceDeal | null = null;

const TOOLTIP_DIV: HTMLDivElement = (
  <div
    style={{
      visibility: "hidden",
      display: "flex",
      flexDirection: "column",
      alignItems: "center",
      justifyContent: "center",
      minHeight: "100px",
      width: "312px",
      cursor: "grab",
      // font
      fontFamily: "Courier New,Arial",
      fontWeight: 600,
      textSize: "14px",
      // appearance
      border: "rgba(64,64,64,0.8) 1px solid",
      backgroundColor: "rgba(0,0,0,0.8)",
      // position
      position: "absolute",
      zIndex: 51,
      top: "0px",
      left: "0px",
      padding: "5px",
    }}
  ></div>
);

type QuickActionType = "medical" | "food" | "armour";
function determineQuickActionType(elem: HTMLElement): QuickActionType | null {
  if (elem.tagName !== "IMG" && elem.tagName !== "DIV") {
    return null;
  }

  let detailsDiv: HTMLDivElement;
  if (
    elem.tagName === "IMG" &&
    (elem as HTMLImageElement).src.match(".*hotrods.*.png$")
  ) {
    detailsDiv = elem.nextElementSibling as HTMLDivElement;
  } else {
    detailsDiv = elem as HTMLDivElement;
  }

  if (detailsDiv.classList.contains("playerHealth")) {
    return "medical";
  } else if (detailsDiv.classList.contains("playerNourishment")) {
    return "food";
  } else if (
    detailsDiv.parentElement &&
    detailsDiv.parentElement.id === "sidebarArmour"
  ) {
    return "armour";
  }

  return null;
}

interface ServiceDeal {
  totalPrice: number;
  itemType: string | null;
  itemPrice: number | null;
  itemTradeId: string | null;
  serviceCategory: string | null;
  servicePrice: number | null;
  serviceIdMember: string | number | null;
}

/**
 * TODO: technically not the best deal since it only considers using 1 item
 */
async function findBestItemService(
  itemPriceData: MarketItemListing[],
  serviceInfos: Record<string, MarketServiceListing>,
  neededPts: number,
  serviceCategory: "food" | "medical"
): Promise<ServiceDeal> {
  const result: ServiceDeal = {
    totalPrice: Infinity,
    itemType: null,
    itemPrice: null,
    itemTradeId: null,
    serviceCategory,
    servicePrice: null,
    serviceIdMember: null,
  };

  for (const item of itemPriceData) {
    const itemTypeFull = item.item;
    const itemTypeList = itemTypeFull.split("_");
    const itemPrice = item.price;
    const itemTradeId = item.trade_id;

    const itemData = globalData[itemTypeList[0]];

    const pts =
      serviceCategory === "food"
        ? itemData["foodrestore"]
        : itemData["healthrestore"];

    if (pts >= neededPts && itemPrice < result.totalPrice) {
      result.itemType = itemTypeFull;
      result.itemPrice = itemPrice;
      result.totalPrice = itemPrice;
      result.itemTradeId = itemTradeId;
      result.serviceIdMember = null;
    } else if (
      pts * 2 >= neededPts &&
      // either medical, or item is uncooked (thus having only 1 part to its type)
      (serviceCategory === "medical" || itemTypeList.length === 1)
    ) {
      const needServiceLevel = Item.getServiceLevel(itemData);
      const serviceInfo = await getCheapestService(
        needServiceLevel,
        null,
        serviceInfos
      );
      if (!serviceInfo) {
        continue;
      }
      if (itemPrice + serviceInfo.price < result.totalPrice) {
        result.totalPrice = itemPrice + serviceInfo.price;
        result.itemType = itemTypeFull;
        result.itemPrice = itemPrice;
        result.itemTradeId = itemTradeId;
        result.servicePrice = serviceInfo.price;
        result.serviceIdMember = serviceInfo.id_member;
      }
    }
  }

  return result;
}

async function executeItemServiceAction(
  executionTaskId: number,
  curPt: any,
  maxPt: any,
  itemCategory: "food" | "medical",
  serviceCategory: "Doctor" | "Chef"
): Promise<void> {
  curPt = parseInt(curPt);
  maxPt = parseInt(maxPt);
  const neededPts = maxPt - curPt;

  if (neededPts <= 0) {
    TOOLTIP_DIV.style.visibility = "hidden";
    return;
  }

  if (!Inventory.hasEmptySlot()) {
    TOOLTIP_DIV.innerHTML = "";
    const span = <span style={{ color: "red" }}>Inventory is full</span>;
    TOOLTIP_DIV.appendChild(span);
    return;
  }

  const bestDeal = await findBestItemService(
    (await fetchMarketData({ category: itemCategory })) as MarketItemListing[],
    await fetchServicePrices(serviceCategory),
    neededPts,
    itemCategory
  );

  if (executionTaskId !== currentTaskId) {
    return;
  }

  if (bestDeal.totalPrice === Infinity) {
    TOOLTIP_DIV.textContent = "No available services";
  } else {
    TOOLTIP_DIV.innerHTML = "";
    const lines = [];
    if (bestDeal.itemTradeId) {
      lines.push(
        <div>
          Buy item {globalData[bestDeal.itemType].name} for{" "}
          {makeColoredPriceBasedOnCash(bestDeal.itemPrice)}
        </div>
      );
    }
    if (bestDeal.serviceIdMember) {
      lines.push(
        <div>
          Buy service for {makeColoredPriceBasedOnCash(bestDeal.servicePrice)}
        </div>
      );
    }
    lines.push(
      <div>Total cost: {makeColoredPriceBasedOnCash(bestDeal.totalPrice)}</div>
    );
    for (const line of lines) {
      TOOLTIP_DIV.appendChild(line);
    }
    currentDeal = bestDeal;
  }
}

async function executeSearch(
  executionTaskId: number,
  quickActionType: QuickActionType
) {
  if (executionTaskId !== currentTaskId) {
    return;
  }

  if (!userVars || !globalData) {
    return;
  }

  // show loading box and fetch data
  TOOLTIP_DIV.textContent = "Loading...";
  TOOLTIP_DIV.style.visibility = "visible";

  if (quickActionType === "food") {
    await executeItemServiceAction(
      executionTaskId,
      userVars["DFSTATS_df_hungerhp"],
      getMaxHunger(),
      "food",
      "Chef"
    );
  } else if (quickActionType === "medical") {
    await executeItemServiceAction(
      executionTaskId,
      userVars["DFSTATS_df_hpcurrent"],
      userVars["DFSTATS_df_hpmax"],
      "medical",
      "Doctor"
    );
  } else if (quickActionType === "armour") {
    if (
      !userVars["DFSTATS_df_armourhp"] ||
      !userVars["DFSTATS_df_armourhpmax"] ||
      !userVars["DFSTATS_df_armourtype"] ||
      userVars["DFSTATS_df_armourhp"] >= userVars["DFSTATS_df_armourhpmax"]
    ) {
      TOOLTIP_DIV.style.visibility = "hidden";
      return;
    }

    const armourRepairLevel = Item.getServiceLevel(
      globalData[userVars["DFSTATS_df_armourtype"].split("_")[0]]
    );

    const service = await getCheapestService(armourRepairLevel, "Engineer");
    if (service) {
      TOOLTIP_DIV.textContent = `Repair: ${service.price}`;
      currentDeal = {
        totalPrice: service.price,
        itemType: null,
        itemTradeId: null,
        itemPrice: null,
        serviceCategory: "armour",
        servicePrice: service.price,
        serviceIdMember: service.id_member,
      };
    } else {
      TOOLTIP_DIV.textContent = "No available services";
    }
  }

  if (currentDeal == null) {
    return;
  }

  TOOLTIP_DIV.appendChild(<br></br>);
  if (getCash() < currentDeal.totalPrice) {
    TOOLTIP_DIV.appendChild(
      <div
        style={{
          color: "red",
          position: "absolute",
          bottom: "0",
          padding: "5px",
          fontSize: "12px",
        }}
      >
        Not enough cash to complete transactions
      </div>
    );
  } else {
    TOOLTIP_DIV.appendChild(
      <div
        style={{
          color: "blue",
          position: "absolute",
          bottom: "0",
          padding: "5px",
          fontSize: "12px",
        }}
      >
        Click while holding 'Ctrl' to complete the transactions
      </div>
    );
  }
}

function resetStates() {
  if (timeout) {
    clearTimeout(timeout);
    timeout = null;
    currentTaskId++;
  }

  currentDeal = null;

  TOOLTIP_DIV.style.visibility = "hidden";
  TOOLTIP_DIV.innerHTML = "";
}

/**
 * need globalData, which is not available in outpost
 */
export function addQuickActionsToSidebar() {
  if (!sidebar) {
    Log.error("sidebar not found");
    return;
  }

  document.body.appendChild(TOOLTIP_DIV);

  sidebar.addEventListener("mousemove", (e) => {
    if (!(e.target instanceof HTMLElement)) {
      return;
    }

    const quickActionType = determineQuickActionType(e.target);
    if (quickActionType == null) {
      resetStates();
      return;
    }

    // need to set position before checking running task
    TOOLTIP_DIV.style.left = e.clientX + 20 + "px";
    TOOLTIP_DIV.style.top = e.clientY - 30 - TOOLTIP_DIV.offsetHeight + "px";

    // if we have a pending task for this item, don't do anything
    if (timeout !== null) {
      return;
    }

    const executionTaskId = currentTaskId;
    timeout = setTimeout(async () => {
      await executeSearch(executionTaskId, quickActionType);
    }, DELAY_SEARCH_ON_HOVER);
  });

  sidebar.addEventListener("mouseleave", () => {
    resetStates();
  });

  sidebar.addEventListener("click", async (e) => {
    if (!currentDeal || !e.ctrlKey) {
      return;
    }

    if (
      currentDeal.serviceCategory === "food" ||
      currentDeal.serviceCategory === "medical"
    ) {
      const itemSlot = await buyItem(
        currentDeal.itemTradeId,
        currentDeal.itemPrice
      );
      if (itemSlot == null) {
        return;
      }
      if (currentDeal.serviceIdMember) {
        await buyService(
          currentDeal.serviceCategory,
          currentDeal.serviceIdMember,
          currentDeal.servicePrice,
          currentDeal.itemType,
          itemSlot
        );
      }
      if (currentDeal.serviceCategory === "food") {
        await useItem(new Slot(itemSlot));
      }
    } else if (currentDeal.serviceCategory === "armour") {
      await buyService(
        currentDeal.serviceCategory,
        currentDeal.serviceIdMember,
        currentDeal.servicePrice,
        userVars["DFSTATS_df_armourtype"],
        Slot.ARMOUR_SLOT
      );
      return;
    }
  });
}
