type GMRequestDetails = Parameters<typeof GM.xmlHttpRequest>[0];
export type GMFetchDetails = Pick<
  GMRequestDetails,
  Exclude<
    keyof GMRequestDetails,
    "onload" | "onerror" | "onabort" | "ontimeout"
  >
>;

export async function gmFetch(details: GMFetchDetails): Promise<string> {
  return new Promise((resolve, reject) => {
    GM.xmlHttpRequest({
      ...details,
      onload: (response) => {
        resolve(response.responseText);
      },
      onerror: (response) => {
        reject({ type: "error", response });
      },
      onabort: (response) => {
        reject({ type: "abort", response });
      },
      ontimeout: (response) => {
        reject({ type: "timeout", response });
      },
    });
  });
}
