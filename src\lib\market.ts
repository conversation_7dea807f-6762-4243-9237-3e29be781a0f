import { getCash, getTradeZone } from "@/lib/common/character";
import { Inventory, Item } from "@/lib/common/inventory-storage";
import { promptOk } from "@/lib/common/prompt";
import { APIFacet, PAGE } from "@/lib/common/webcall";

const SERVICE_CATEGORIES = ["Doctor", "Chef", "Engineer"];

const CACHE_EXPIRATION = 5 * 1000; // 5 seconds

interface MarketCacheEntry {
  data: any;
  timestamp: number;
}
const MARKET_CACHE: Record<string, MarketCacheEntry> = {};

/**
 * @param tradezone see tradezoneNamer and tradezoneNamerShort in onlinezombiemmo-js/base.js
 */
export interface SearchCriteria {
  searchname?: string;
  category?: string;
  tradezone?: number | string;
}

export type MarketCategory =
  | "armour"
  | "weapon_melee"
  | "weapon_pistol"
  | "weapon_rifle"
  | "weapon_shotgun"
  | "weapon_lightmachinegun"
  | "weapon_heavymachinegun"
  | "weapon_grenadelauncher"
  | "ammo_handgun"
  | "ammo_rifle"
  | "ammo_shotgun"
  | "ammo_grenade"
  | "ammo_special"
  | "food"
  | "medical"
  | "clothing_basic"
  | "clothing_coat"
  | "clothing_headwear"
  | "barricading"
  | "misc"
  | "Chef"
  | "Doctor"
  | "Engineer"
  | "credits"
  | "implants"
  | "backpack";

/**
 * can be used in many places
 * data format:
 *  always present:
 *      done: "1"
 *      tradelist_maxresults: "<num>"
 *      then a list of result entries, index starts from 0 and ends with tradelist_maxresults - 1
 *
 *  item results
 *      tradelist_<index>_trade_id: "<id>"
 *      tradelist_<index>_id_member: "<id>"
 *      tradelist_<index>_member_name: "<name>"
 *      tradelist_<index>_id_member_to: "0"
 *      tradelist_<index>_member_to_name: ""
 *      tradelist_<index>_item: "<item>"          this is the "type" of the item, i.e. including stats, name, etc
 *      tradelist_<index>_itemname: "<name>"
 *      tradelist_<index>_price: "<price>"
 *      tradelist_<index>_trade_zone: "<tradezone>"
 *      tradelist_<index>_category: "<category>"
 *      tradelist_<index>_quantity: "<quantity>"
 *      tradelist_<index>_priceper: "<priceper>"
 *      tradelist_<index>_deny_private: "0"
 *
 *  service results
 *      tradelist_<index>_available: "1"
 *      tradelist_<index>_id_member: "<id>"       this is the "trade_id" equivalent of item results. this needs to be put in the buynum field when sending request
 *      tradelist_<index>_level: "<level>"
 *      tradelist_<index>_member_name: "<name>"
 *      tradelist_<index>_price: "<price>"
 *      tradelist_<index>_profession: "<profession>"
 *      tradelist_<index>_trade_zone: "<tradezone>"
 */
export async function fetchMarketData(
  searchCriteria: SearchCriteria | Item
): Promise<MarketListing[]> {
  if (searchCriteria instanceof Item) {
    searchCriteria = {
      searchname: searchCriteria.data["name"],
      category: searchCriteria.marketCategory,
    };
  }

  if (!searchCriteria.searchname && !searchCriteria.category) {
    throw new Error("fetchMarketData: no search criteria");
  }

  // lookup cache
  const cacheKey = JSON.stringify(searchCriteria);
  const cachedData = MARKET_CACHE[cacheKey];
  if (cachedData) {
    if (Date.now() - cachedData.timestamp < CACHE_EXPIRATION) {
      return cachedData.data;
    }
  }

  const dataArr = {
    tradezone: searchCriteria.tradezone ?? getTradeZone(),
    searchname: searchCriteria.searchname,
    memID: "",
  };

  const isService = SERVICE_CATEGORIES.includes(searchCriteria.category);

  if (isService) {
    dataArr["profession"] = searchCriteria.category;
    dataArr["category"] = "";
    dataArr["searchtype"] = "buyinglist";
    dataArr["search"] = "services";
  } else {
    dataArr["profession"] = "";
    dataArr["category"] = searchCriteria.category;

    if (searchCriteria.searchname) {
      dataArr["searchtype"] = "buyinglistitemname";
    }
    if (searchCriteria.category) {
      dataArr["searchtype"] = "buyinglistcategory";
    }
    if (searchCriteria.searchname && searchCriteria.category) {
      dataArr["searchtype"] = "buyinglistcategoryitemname";
    }
    dataArr["search"] = "trades";
  }

  const dataStr = await APIFacet.call(fetchMarketData, "trade_search", dataArr);
  const data = flshToArr(dataStr, "");
  MARKET_CACHE[cacheKey] = {
    data,
    timestamp: Date.now(),
  };
  return searchResultsToArray(data, isService);
}

export interface MarketItemListing {
  listingType: "item";
  trade_id: string;
  id_member: number;
  member_name: string;
  id_member_to: number;
  member_to_name: string;
  item: string; // this is the uniquely identifying item type
  itemname: string;
  price: number;
  trade_zone: number;
  category: string;
  quantity: number;
  priceper: number;
  deny_private: boolean;
}

export interface MarketServiceListing {
  listingType: "service";
  id_member: number;
  member_name: string;
  price: number;
  trade_zone: number;
  profession: string;
  available: boolean;
  level: number;
}

export type MarketListing = MarketServiceListing | MarketItemListing;

export function searchResultsToArray(
  searchResults: Record<string, any>,
  isService: boolean
): MarketListing[] {
  const result: MarketListing[] = [];

  for (let i = 0; i < parseInt(searchResults["tradelist_maxresults"]); i++) {
    if (isService) {
      result.push({
        listingType: "service",
        id_member: parseInt(searchResults[`tradelist_${i}_id_member`]),
        member_name: searchResults[`tradelist_${i}_member_name`],
        price: parseInt(searchResults[`tradelist_${i}_price`]),
        trade_zone: parseInt(searchResults[`tradelist_${i}_trade_zone`]),
        profession: searchResults[`tradelist_${i}_profession`],
        available: searchResults[`tradelist_${i}_available`] === "1",
        level: parseInt(searchResults[`tradelist_${i}_level`]),
      });
    } else {
      result.push({
        listingType: "item",
        trade_id: searchResults[`tradelist_${i}_trade_id`],
        id_member: parseInt(searchResults[`tradelist_${i}_id_member`]),
        member_name: searchResults[`tradelist_${i}_member_name`],
        id_member_to: parseInt(searchResults[`tradelist_${i}_id_member_to`]),
        member_to_name: searchResults[`tradelist_${i}_member_to_name`],
        item: searchResults[`tradelist_${i}_item`],
        itemname: searchResults[`tradelist_${i}_itemname`],
        price: parseInt(searchResults[`tradelist_${i}_price`]),
        trade_zone: parseInt(searchResults[`tradelist_${i}_trade_zone`]),
        category: searchResults[`tradelist_${i}_category`],
        quantity: parseInt(searchResults[`tradelist_${i}_quantity`]),
        priceper: parseInt(searchResults[`tradelist_${i}_priceper`]),
        deny_private: searchResults[`tradelist_${i}_deny_private`] === "1",
      });
    }
  }

  return result;
}

/**
 * @param category one of {@link SERVICE_CATEGORIES}
 * @returns a map of level to service info
 */
export async function fetchServicePrices(
  category: string
): Promise<Record<string, MarketServiceListing>> {
  const results = await fetchMarketData({ category });

  const mapping: Record<string, MarketServiceListing> = {};

  for (const result of results) {
    if (result.listingType !== "service") {
      continue;
    }
    const level = result.level;
    if (!mapping[level] || result.price < mapping[level].price) {
      mapping[level] = result;
    }
  }

  return mapping;
}

export async function getCheapestService(
  minLevel: number,
  category?: string,
  servicePrices?: Record<string, MarketServiceListing>
): Promise<MarketServiceListing | null> {
  if (!category && !servicePrices) {
    throw new Error("getCheapestService: no category and no servicePrices");
  }

  if (!servicePrices) {
    servicePrices = await fetchServicePrices(category);
  }

  let minPrice = Infinity;
  let info = null;
  for (const serviceLevel in servicePrices) {
    if (
      parseInt(serviceLevel) >= minLevel &&
      servicePrices[serviceLevel].price < minPrice
    ) {
      minPrice = servicePrices[serviceLevel].price;
      info = servicePrices[serviceLevel];
    }
  }
  return info;
}

export async function buyService(
  // this is the market category of the service
  category: string,
  idMember: number | string,
  price: number | string,
  // item to use for service
  itemtype: string,
  itemSlot: number | string
): Promise<any> {
  idMember = idMember.toString();

  const action = {
    food: "buycook",
    medical: "buyadminister",
    armour: "buyrepair",
  }[category];
  if (!action) {
    throw new Error(`buyService: unknown category ${category}`);
  }

  if (getCash() < parseInt(price as any)) {
    promptOk("Unable to buy service: Not enough cash");
    return;
  }

  const dataArr = {};

  dataArr["creditsnum"] = 0;
  dataArr["renameto"] = "undefined`undefined";
  dataArr["buynum"] = idMember;
  dataArr["expected_itemprice"] = price.toString();
  dataArr["expected_itemtype2"] = "";
  dataArr["expected_itemtype"] = itemtype;
  dataArr["itemnum2"] = "0";
  dataArr["itemnum"] = itemSlot.toString();
  dataArr["price"] = scrapAmount(itemtype, 1);
  dataArr["action"] = action;

  const results = await APIFacet.call(buyService, "inventory_new", dataArr);

  updateIntoArr(flshToArr(results, "DFSTATS_"), userVars);
  populateInventory();
  populateCharacterInventory();
  updateAllFields();

  return results;
}
APIFacet.registerAPI(buyService, [PAGE.MARKETPLACE]);

/**
 * either pass in a MarketItemListing, or trade_id and price
 * @returns slot number if successfully bought, null otherwise
 */
export async function buyItem(
  arg1: string | MarketItemListing,
  arg2?: number | string
): Promise<number | null> {
  let tradeId: string;
  let price: number | string;
  if (typeof arg1 === "string") {
    tradeId = arg1;
    price = arg2;
    if (!price) {
      throw new Error("buyItem: price is required");
    }
  } else {
    tradeId = arg1.trade_id;
    price = arg1.price;
  }

  if (!Inventory.hasEmptySlot()) {
    promptOk("Unable to buy item: Inventory is full");
    return null;
  }

  if (getCash() < parseInt(price as any)) {
    promptOk("Unable to buy item: Not enough cash");
    return null;
  }

  const dataArray = {};
  dataArray["creditsnum"] = "undefined";
  dataArray["buynum"] = tradeId;
  dataArray["renameto"] = "undefined`undefined";
  dataArray["expected_itemprice"] = price.toString();
  dataArray["expected_itemtype2"] = "";
  dataArray["expected_itemtype"] = "";
  dataArray["itemnum2"] = 0;
  dataArray["itemnum"] = 0;
  dataArray["price"] = 0;
  dataArray["action"] = "newbuy";

  const results = await APIFacet.call(buyItem, "inventory_new", dataArray);

  if (results === "error=missed") {
    promptOk("You have missed this item.");
    return null;
  }

  const flshArr = flshToArr(results, "DFSTATS_");
  let returnSlot: number = null;
  for (let key in flshArr) {
    if (key.startsWith("DFSTATS_df_inv") && key.endsWith("_type")) {
      returnSlot = parseInt(key.split("_")[2].substring(3));
      break;
    }
  }
  updateIntoArr(flshArr, userVars);

  if (unsafeWindow.populateInventory) {
    populateInventory();
  }
  updateAllFields();

  return returnSlot;
}
APIFacet.registerAPI(buyItem, [PAGE.MARKETPLACE]);
