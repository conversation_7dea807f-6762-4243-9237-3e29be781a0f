import { Storage, loadInventoryData } from "@/lib/common/inventory-storage";
import { Log } from "@/lib/common/log";
import { hash, scrapValue } from "@/lib/common/polyfill";
import { getCurrentPage } from "./lib/common/util";

import { launchStandaloneGame } from "@/lib/config/auto-launch-standalone";
import { initConsoleUse } from "@/lib/config/console-use";
import { addMoveStorageButtons } from "@/lib/config/move-storage";
import { enhanceOutpostButtons } from "@/lib/config/outpost-enhancement";
import { augmentInfoCardWithPriceInfo } from "@/lib/config/price-info-card";
import {
  loadGlobalVarsFromLocalStorage,
  saveGlobalVarsToLocalStorageOnUnload,
} from "@/lib/config/save-load-globalvars";
import { addCheckSEScrapServiceButton } from "@/lib/config/scrap-service";
import { addQuickActionsToSidebar } from "@/lib/config/sidebar-quick-actions";

const CONFIGS: Record<string, Array<() => void | Promise<void>>> = {
  // all pages except innercity inventory
  "index\\.php": [
    initConsoleUse,
    saveGlobalVarsToLocalStorageOnUnload,
    addCheckSEScrapServiceButton,
  ],
  // outpost
  "index\\.php$": [
    loadGlobalVarsFromLocalStorage,
    loadInventoryData,
    enhanceOutpostButtons,
    addQuickActionsToSidebar,
  ],
  // yard
  "index\\.php\\?page=24": [
    Storage.loadStorageData,
    addMoveStorageButtons,
    augmentInfoCardWithPriceInfo,
  ],
  // outpost inventory
  "index\\.php\\?page=25": [
    Storage.loadStorageData,
    addMoveStorageButtons,
    augmentInfoCardWithPriceInfo,
    addQuickActionsToSidebar,
  ],
  // innercity inventory
  "DF3D_InventoryPage\\.php\\?page=31": [augmentInfoCardWithPriceInfo],
  // marketplace
  "index\\.php\\?page=35": [
    Storage.loadStorageData,
    addMoveStorageButtons,
    augmentInfoCardWithPriceInfo,
    addQuickActionsToSidebar,
  ],
  // storage
  "index\\.php\\?page=50": [
    augmentInfoCardWithPriceInfo,
    addQuickActionsToSidebar,
  ],
  // innercity / standalone launch page
  "index\\.php\\?page=21": [launchStandaloneGame],
};

async function setup() {
  // polyfill
  if (!unsafeWindow.hash) unsafeWindow.hash = hash;
  if (!unsafeWindow.scrapValue) unsafeWindow.scrapValue = scrapValue;

  let currentPage = getCurrentPage();
  Log.info("applying config for", currentPage);

  for (const regex in CONFIGS) {
    if (!currentPage.match(regex)) {
      continue;
    }
    Log.info("rule matched", regex);

    for (const f of CONFIGS[regex]) {
      Log.info("applying config", f.name);
      await f();
      Log.info("config applied", f.name);
    }
  }

  Log.info("finished setup");
}

if (document.readyState === "complete") {
  setup();
} else {
  window.addEventListener("load", setup);
}

import { initTest } from "./test";
initTest();
