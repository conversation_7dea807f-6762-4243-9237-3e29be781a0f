import { Slot, swapSlot } from "./lib/common/inventory-storage";
import { Storage } from "./lib/common/inventory-storage";
import { buyItem, fetchMarketData } from "./lib/market";
import { promptOk } from "./lib/common/prompt";

function suppressConnectionErrorRefresh() {
  unsafeWindow.webCallError = () => {
    console.log("webCallError called, suppressing refresh");
  };

  unsafeWindow.fetchMarketData = fetchMarketData;
  unsafeWindow.buyItem = buyItem;
  unsafeWindow.promptOk = promptOk;
}

export function initTest() {
  suppressConnectionErrorRefresh();
}
